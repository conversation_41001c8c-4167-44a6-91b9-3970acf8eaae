import { NextRequest } from 'next/server';
import { headers } from 'next/headers';
import { streamText } from 'ai';
import { auth } from '@/lib/auth';
import { getModel, DEFAULT_MODEL } from '@/lib/openrouter';
import { createChat, addMessage, getChatHistory } from '@/lib/chat';
import { checkChatLimit, incrementChatUsage } from '@/lib/chat-usage';
import { modelDetails } from '@/app/provider/models';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return Response.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 检查用户聊天限额
    const limitCheck = await checkChatLimit(session.user.id);
    if (!limitCheck.canChat) {
      return Response.json(
        {
          error: '已达到今日使用限额，请明天再试',
          details: `您今日已使用 ${limitCheck.usedCount}/${limitCheck.totalLimit} 次免费对话，请明天再试。`,
          code: 'CHAT_LIMIT_EXCEEDED',
          usedCount: limitCheck.usedCount,
          totalLimit: limitCheck.totalLimit,
          remainingCount: limitCheck.remainingCount
        },
        { status: 429 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { messages, chatId, modelId } = body;

    if (!messages || messages.length === 0) {
      return Response.json(
        { error: '消息不能为空' },
        { status: 400 }
      );
    }

    let currentChatId = chatId;
    const userMessage = messages[messages.length - 1];

    // 验证 chatId 是否为有效的 UUID 格式
    const isValidUUID = (id: string) => {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      return uuidRegex.test(id);
    };

    // 如果没有 chatId 或 chatId 不是有效的 UUID，创建新对话
    if (!currentChatId || !isValidUUID(currentChatId)) {
      // 使用固定的"新的聊天"标题，避免显示用户消息内容作为临时标题
      const newChat = await createChat(session.user.id, '新的聊天');
      currentChatId = newChat.id;
    } else {
      // 验证现有的 chatId 是否存在于数据库中
      const existingChat = await getChatHistory(currentChatId, session.user.id);
      if (existingChat.length === 0) {
        // 对话不存在，使用客户端提供的ID创建新对话，使用固定标题
        await createChat(session.user.id, '新的聊天', currentChatId);
      }
    }

    // 保存用户消息
    await addMessage(currentChatId, session.user.id, 'user', userMessage.content);

    // 增加用户的聊天使用次数
    await incrementChatUsage(session.user.id);

    // 获取对话历史（包括刚保存的用户消息）
    const chatHistory = await getChatHistory(currentChatId, session.user.id);

    // 获取 AI 模型 - 根据前端传递的 modelId 选择模型
    let actualModelId: string;
    if (modelId && modelDetails[modelId]) {
      // 使用 modelDetails 中的实际模型 ID
      actualModelId = modelDetails[modelId].id;
    } else {
      // 回退到默认模型
      const defaultModelKey = Object.keys(modelDetails)[0];
      actualModelId = defaultModelKey ? modelDetails[defaultModelKey].id : DEFAULT_MODEL;
    }

    const model = getModel(actualModelId);

    // 使用 Vercel AI SDK 的标准流式响应
    const result = streamText({
      model,
      messages: chatHistory,
      temperature: 0.7,
      maxOutputTokens: 2000,
      async onFinish({ text }) {
        try {
          // 保存完整的助手回复到数据库
          await addMessage(currentChatId, session.user.id, 'assistant', text);

          // 智能标题生成现在由前端处理，这里不再需要
        } catch (error) {
          console.error('保存助手消息失败:', error);
          // 不抛出错误，避免影响流式响应
        }
      },
    });

    return result.toUIMessageStreamResponse({
      headers: {
        'X-Chat-Id': currentChatId,
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('聊天 API 错误:', error);
    return Response.json(
      {
        error: '服务器内部错误',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
