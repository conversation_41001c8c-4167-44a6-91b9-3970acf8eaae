import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import { getChatWithMessages, deleteChat, updateChatTitle } from '@/lib/chat';

/**
 * 获取单个对话详情
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = await params;

    if (!chatId || typeof chatId !== 'string') {
      return NextResponse.json(
        { error: '无效的对话 ID' },
        { status: 400 }
      );
    }

    // 获取对话详情
    const chat = await getChatWithMessages(chatId, session.user.id);

    if (!chat) {
      return NextResponse.json(
        { error: '对话不存在或无权访问' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      chat: {
        id: chat.id,
        title: chat.title,
        createdAt: chat.createdAt.toISOString(),
        updatedAt: chat.updatedAt.toISOString(),
        messages: chat.messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          createdAt: msg.createdAt.toISOString(),
        })),
      },
    });

  } catch (error) {
    console.error('获取对话详情错误:', error);
    return NextResponse.json(
      { error: '获取对话详情失败' },
      { status: 500 }
    );
  }
}

/**
 * 更新对话标题
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = await params;

    if (!chatId || typeof chatId !== 'string') {
      return NextResponse.json(
        { error: '无效的对话 ID' },
        { status: 400 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { title } = body;

    if (!title || typeof title !== 'string' || title.trim().length === 0) {
      return NextResponse.json(
        { error: '对话标题不能为空' },
        { status: 400 }
      );
    }

    // 更新对话标题
    const success = await updateChatTitle(chatId, session.user.id, title.trim());

    if (!success) {
      return NextResponse.json(
        { error: '对话不存在或无权访问' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: '对话标题更新成功',
    });

  } catch (error) {
    console.error('更新对话标题错误:', error);
    return NextResponse.json(
      { error: '更新对话标题失败' },
      { status: 500 }
    );
  }
}

/**
 * 删除对话
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = await params;

    if (!chatId || typeof chatId !== 'string') {
      return NextResponse.json(
        { error: '无效的对话 ID' },
        { status: 400 }
      );
    }

    // 删除对话
    const success = await deleteChat(chatId, session.user.id);

    if (!success) {
      return NextResponse.json(
        { error: '对话不存在或无权访问' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: '对话删除成功',
    });

  } catch (error) {
    console.error('删除对话错误:', error);
    return NextResponse.json(
      { error: '删除对话失败' },
      { status: 500 }
    );
  }
}
