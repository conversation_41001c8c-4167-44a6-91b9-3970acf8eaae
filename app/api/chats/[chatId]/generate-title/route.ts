import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import { getChatWithMessages, updateChatTitle, generateSmartChatTitle } from '@/lib/chat';

/**
 * 智能生成对话标题
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = await params;

    if (!chatId || typeof chatId !== 'string') {
      return NextResponse.json(
        { error: '无效的对话 ID' },
        { status: 400 }
      );
    }

    // 获取对话详情和消息
    const chat = await getChatWithMessages(chatId, session.user.id);

    if (!chat) {
      return NextResponse.json(
        { error: '对话不存在或无权访问' },
        { status: 404 }
      );
    }

    // 检查是否有足够的消息来生成标题
    if (!chat.messages || chat.messages.length < 2) {
      return NextResponse.json(
        { error: '对话消息不足，无法生成智能标题' },
        { status: 400 }
      );
    }

    // 获取第一条用户消息和第一条助手回复
    const userMessage = chat.messages.find(msg => msg.role === 'user');
    const assistantMessage = chat.messages.find(msg => msg.role === 'assistant');

    if (!userMessage || !assistantMessage) {
      return NextResponse.json(
        { error: '缺少必要的对话内容' },
        { status: 400 }
      );
    }

    // 生成智能标题
    const smartTitle = await generateSmartChatTitle(
      userMessage.content,
      assistantMessage.content
    );

    // 更新对话标题
    const success = await updateChatTitle(chatId, session.user.id, smartTitle);

    if (!success) {
      return NextResponse.json(
        { error: '更新对话标题失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      title: smartTitle,
      message: '智能标题生成成功',
    });

  } catch (error) {
    console.error('智能标题生成错误:', error);
    return NextResponse.json(
      { error: '智能标题生成失败' },
      { status: 500 }
    );
  }
}
