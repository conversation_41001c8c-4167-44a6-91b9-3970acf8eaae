'use client';

import { useState, useCallback, useMemo, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useChat } from '@ai-sdk/react';
import { MessageList } from './message-list';
import { ChatInput } from './chat-input';
import { useChatDetail } from '@/lib/hooks/use-chats';
import { usePromptFromUrl } from '@/lib/hooks/use-prompt-from-url';
import { toast } from 'sonner';
import { DefaultChatTransport } from 'ai';
import { useChatStore } from '@/lib/stores/chat-store';

interface ChatContentProps {
  initialChatId?: string;
}

export function ChatContent({ initialChatId }: ChatContentProps) {
  const router = useRouter();
  const params = useParams();
  const [generatedChatId, setGeneratedChatId] = useState<string>('');
  const { addPendingChat, removePendingChat, selectedModel, setSelectedModel, generateSmartTitle } = useChatStore();

  // 处理 URL 中的 prompt 参数
  const { prompt: urlPrompt, isLoading: isLoadingPrompt, hasPromptParam } = usePromptFromUrl();

  // 从 URL 参数获取当前 chatId，优先使用 URL 参数
  const urlChatId = params?.chatId as string;
  const currentChatId = urlChatId || initialChatId;

  // 为新对话生成稳定的 chatId
  const effectiveChatId = useMemo(() => {
    if (currentChatId) {
      return currentChatId;
    }
    if (!generatedChatId) {
      const newId = crypto.randomUUID();
      setGeneratedChatId(newId);
      return newId;
    }
    return generatedChatId;
  }, [currentChatId, generatedChatId]);

  // 使用 React Query 获取聊天详情
  const {
    initialMessages,
    isLoading: isLoadingMessages,
    error: chatDetailError,
  } = useChatDetail(currentChatId);

  // 使用 useChat hook - 统一配置，使用稳定的 effectiveChatId
  const {
    messages,
    // input,
    // handleInputChange,
    // handleSubmit,
    // setInput,
    // append,
    sendMessage,
    status,
    stop,
    error,
    // reload,
  } = useChat({
    id: effectiveChatId, // 使用稳定的 effectiveChatId 作为 hook 的 id
    messages: initialMessages,
    // body: {
    //   chatId: effectiveChatId,
    //   modelId: selectedModel, // 传递选中的模型ID
    // },
    // maxSteps: 1,
    transport: new DefaultChatTransport({
      api: '/api/chat',
      credentials: 'include',
      // headers: { 'Custom-Header': 'value' },
    }),
    experimental_throttle: 50,
    onFinish: async (message) => {
      try {
        const baseMessageCount = initialMessages?.length || 0;

        // 检查是否是首次对话：如果基础消息数为0，说明这是新对话的第一轮
        const isFirstConversation = baseMessageCount === 0;
        if (isFirstConversation) {
          generateSmartTitle(effectiveChatId);
        }

      } catch (error) {
        console.error('❌ 处理聊天完成事件失败:', error);
        // 出错时回退到移除临时记录
        removePendingChat(effectiveChatId);
      }
    },
    onError: (error) => {
      // 移除临时聊天记录（如果存在）
      removePendingChat(effectiveChatId);

      // 检查是否是限额错误
      if (error.message && (
        error.message.includes('已达到今日使用限额') ||
        error.message.includes('已达到今日免费使用限制')
      )) {
      } else {
        toast.error('发送消息失败，请重试');
      }
    },
  });



  // 处理 URL 中的 prompt 参数 - 成功获取后清理 URL
  useEffect(() => {
    if (urlPrompt && hasPromptParam && !isLoadingPrompt) {
      // 成功获取到 prompt 数据后，清理 URL 参数以避免重复触发
      console.log('✅ 成功获取 URL prompt，清理 URL 参数');
      router.replace('/chat', { scroll: false });
    }
  }, [urlPrompt, hasPromptParam, isLoadingPrompt, router]);

  // 处理表单提交
  const handleFormSubmit = useCallback((e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // 如果是新对话，添加临时聊天记录到侧边栏
    if (!urlChatId) {
      addPendingChat(effectiveChatId, '新的对话');
      // 立即跳转到新对话页面
      router.replace(`/chat/${effectiveChatId}`);
    }

    // 调用原始的 handleSubmit
    handleSubmit(e);
  }, [handleSubmit, router, effectiveChatId, urlChatId, addPendingChat]);

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* 消息列表 */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <MessageList
          messages={messages}
          isLoading={isLoadingMessages}
          error={error || chatDetailError}
          status={status}
          onRetry={reload}
        />
      </div>

      {/* 输入区域 */}
      <div className="border-t bg-background p-4 flex-shrink-0">
        <div className="max-w-4xl mx-auto">
          <ChatInput
            input={input}
            handleInputChange={handleInputChange}
            handleSubmit={handleFormSubmit}
            setInput={setInput}
            append={append}
            status={status}
            isLoading={status === 'submitted'}
            isStreaming={status === 'streaming'}
            onStop={stop}
            placeholder={urlChatId ? "开始对话，输入 / 快捷选择 Prompt" : "开始对话，输入 / 快捷选择 Prompt"}
            selectedModel={selectedModel}
            onModelChange={setSelectedModel}
            initialPrompt={urlPrompt}
          />
        </div>
      </div>
    </div>
  );
}
