import { db } from '@/db';
import { chats, messages, type Chat, type Message, type NewChat, type NewMessage } from '@/db/schema';
import { eq, desc, and } from 'drizzle-orm';
import type { ChatWithMessages, ChatResponse } from '@/types/chat';
import { getModel } from '@/lib/openrouter';
import { generateText } from 'ai';

/**
 * 创建新的对话
 */
export async function createChat(userId: string, title: string, customId?: string): Promise<Chat> {
  const newChat: NewChat = {
    title,
    userId,
    ...(customId && { id: customId }), // 如果提供了自定义ID，则使用它
  };

  const [chat] = await db.insert(chats).values(newChat).returning();
  return chat;
}

/**
 * 获取用户的所有对话列表
 */
export async function getUserChats(userId: string): Promise<ChatResponse[]> {
  const userChats = await db
    .select({
      id: chats.id,
      title: chats.title,
      createdAt: chats.createdAt,
      updatedAt: chats.updatedAt,
    })
    .from(chats)
    .where(eq(chats.userId, userId))
    .orderBy(desc(chats.updatedAt));

  // 获取每个对话的消息统计和最后一条消息
  const chatResponses: ChatResponse[] = [];
  
  for (const chat of userChats) {
    const chatMessages = await db
      .select({
        content: messages.content,
        createdAt: messages.createdAt,
      })
      .from(messages)
      .where(eq(messages.chatId, chat.id))
      .orderBy(desc(messages.createdAt))
      .limit(1);

    chatResponses.push({
      id: chat.id,
      title: chat.title,
      createdAt: chat.createdAt.toISOString(),
      updatedAt: chat.updatedAt.toISOString(),
      messageCount: chatMessages.length,
      lastMessage: chatMessages[0]?.content,
    });
  }

  return chatResponses;
}

/**
 * 获取单个对话及其消息
 */
export async function getChatWithMessages(chatId: string, userId: string): Promise<ChatWithMessages | null> {
  // 首先验证对话是否属于当前用户
  const chat = await db.query.chats.findFirst({
    where: and(eq(chats.id, chatId), eq(chats.userId, userId)),
  });

  if (!chat) {
    return null;
  }

  // 获取对话的所有消息
  const chatMessages = await db
    .select()
    .from(messages)
    .where(eq(messages.chatId, chatId))
    .orderBy(messages.createdAt);

  return {
    ...chat,
    messages: chatMessages,
  };
}

/**
 * 添加消息到对话
 */
export async function addMessage(
  chatId: string,
  userId: string,
  role: 'user' | 'assistant',
  content: string
): Promise<Message> {
  const newMessage: NewMessage = {
    chatId,
    userId,
    role,
    content,
  };

  const [message] = await db.insert(messages).values(newMessage).returning();

  // 更新对话的 updatedAt 时间
  await db
    .update(chats)
    .set({ updatedAt: new Date() })
    .where(eq(chats.id, chatId));

  return message;
}

/**
 * 删除对话
 */
export async function deleteChat(chatId: string, userId: string): Promise<boolean> {
  const result = await db
    .delete(chats)
    .where(and(eq(chats.id, chatId), eq(chats.userId, userId)))
    .returning();

  return result.length > 0;
}

/**
 * 删除用户的所有对话
 */
export async function deleteAllUserChats(userId: string): Promise<number> {
  const result = await db
    .delete(chats)
    .where(eq(chats.userId, userId))
    .returning();

  return result.length;
}

/**
 * 更新对话标题
 */
export async function updateChatTitle(chatId: string, userId: string, title: string): Promise<boolean> {
  const result = await db
    .update(chats)
    .set({ title, updatedAt: new Date() })
    .where(and(eq(chats.id, chatId), eq(chats.userId, userId)))
    .returning();

  return result.length > 0;
}

/**
 * 获取对话的消息历史（用于 AI 上下文）
 */
export async function getChatHistory(chatId: string, userId: string, limit: number = 20): Promise<Array<{ role: 'user' | 'assistant'; content: string }>> {
  // 验证对话权限
  const chat = await db.query.chats.findFirst({
    where: and(eq(chats.id, chatId), eq(chats.userId, userId)),
  });

  if (!chat) {
    return [];
  }

  const chatMessages = await db
    .select({
      role: messages.role,
      content: messages.content,
    })
    .from(messages)
    .where(eq(messages.chatId, chatId))
    .orderBy(messages.createdAt)
    .limit(limit);

  return chatMessages;
}

/**
 * 智能生成对话标题（基于用户消息和AI回复）
 */
export async function generateSmartChatTitle(
  userMessage: string,
  assistantMessage: string
): Promise<string> {
  try {
    // 获取用于标题生成的模型
    const renameModel = process.env.CHAT_RENAME_MODEL || 'openai/gpt-4o-mini';
    const model = getModel(renameModel);

    // 构建标题生成的提示词
    const titlePrompt = `请基于以下对话内容，生成一个简洁、准确的中文标题（不超过20个字符）：

用户：${userMessage.substring(0, 200)}
助手：${assistantMessage.substring(0, 200)}

要求：
1. 标题要简洁明了，不超过20个字符
2. 准确概括对话的主要内容或主题
3. 使用中文
4. 不要包含引号或其他标点符号
5. 直接返回标题内容，不要其他解释

标题：`;

    // 调用AI模型生成标题
    const response = await generateText({
      model: model,
      prompt: titlePrompt,
      temperature: 0.3,
      maxOutputTokens: 50,
    });

    // 清理生成的标题
    let generatedTitle = response.text.trim();

    // 移除可能的引号和多余的标点
    generatedTitle = generatedTitle.replace(/^["'「『]|["'」』]$/g, '');
    generatedTitle = generatedTitle.replace(/^标题[:：]\s*/, '');

    // 限制长度
    if (generatedTitle.length > 20) {
      generatedTitle = generatedTitle.substring(0, 20);
    }

    // 如果生成的标题为空或无效，保持"新的聊天"标题
    if (!generatedTitle || generatedTitle.length < 2) {
      console.warn('AI生成的标题无效，保持"新的聊天"标题');
      return '新的对话';
    }
    return generatedTitle;

  } catch (error) {
    console.error('❌ 智能标题生成失败:', error);
    return '新的对话';
  }
}
