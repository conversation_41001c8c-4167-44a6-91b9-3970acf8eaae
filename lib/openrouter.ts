import { openai } from '@ai-sdk/openai';
import { createOpenAI } from '@ai-sdk/openai';

// 创建 OpenRouter 客户端
export const openrouter = createOpenAI({
  apiKey: process.env.OPENROUTER_APIKEY,
  baseURL: 'https://openrouter.ai/api/v1',
  headers: {
    'HTTP-Referer': process.env.BETTER_AUTH_URL || 'http://localhost:3000',
    'X-Title': 'PromptRepo Chat',
  },
});

// 默认使用的模型 - 与 models.ts 中的第一个模型保持一致
export const DEFAULT_MODEL = 'openai/gpt-4.1-mini';

// 获取模型实例
export function getModel(modelName: string = DEFAULT_MODEL) {
  return openrouter(modelName);
}

// 格式化消息为 OpenRouter 格式
export function formatMessagesForOpenRouter(messages: Array<{ role: 'user' | 'assistant'; content: string }>) {
  return messages.map(msg => ({
    role: msg.role,
    content: msg.content,
  }));
}
