import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useMemo } from 'react';
import { toast } from 'sonner';
import type { ChatResponse, ChatWithMessages } from '@/types/chat';
import type { Message } from '@ai-sdk/react';

/**
 * 聊天列表查询的 key 工厂函数
 */
export const chatKeys = {
  all: ['chats'] as const,
  lists: () => [...chatKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...chatKeys.lists(), { filters }] as const,
  details: () => [...chatKeys.all, 'detail'] as const,
  detail: (id: string) => [...chatKeys.details(), id] as const,
};

/**
 * 获取聊天列表的 API 函数
 */
async function fetchChats(): Promise<ChatResponse[]> {
  const response = await fetch('/api/chats');

  if (!response.ok) {
    throw new Error('获取对话列表失败');
  }

  const data = await response.json();
  return data.chats || [];
}

/**
 * 获取单个聊天详情的 API 函数
 */
async function fetchChatDetail(chatId: string): Promise<ChatWithMessages | null> {
  const response = await fetch(`/api/chats/${chatId}`);

  if (response.status === 404) {
    // 对话不存在，返回 null
    return null;
  }

  if (!response.ok) {
    throw new Error('获取对话详情失败');
  }

  const data = await response.json();
  return data.chat || null;
}

/**
 * 删除聊天的 API 函数
 */
async function deleteChatApi(chatId: string): Promise<void> {
  const response = await fetch(`/api/chats/${chatId}`, {
    method: 'DELETE',
  });
  
  if (!response.ok) {
    throw new Error('删除对话失败');
  }
}

/**
 * 更新聊天标题的 API 函数
 */
async function updateChatTitleApi(chatId: string, title: string): Promise<ChatResponse> {
  const response = await fetch(`/api/chats/${chatId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ title }),
  });

  if (!response.ok) {
    throw new Error('更新对话标题失败');
  }

  return response.json();
}

/**
 * 智能生成聊天标题的 API 函数
 */
async function generateSmartTitleApi(chatId: string): Promise<{ title: string }> {
  const response = await fetch(`/api/chats/${chatId}/generate-title`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('智能标题生成失败');
  }

  return response.json();
}

/**
 * 聊天列表管理的自定义 hook
 */
export function useChats(currentChatId?: string) {
  const queryClient = useQueryClient();

  // 获取聊天列表的查询
  const {
    data: chats = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: chatKeys.lists(),
    queryFn: fetchChats,
    staleTime: 1000 * 60 * 5, // 5分钟内认为数据是新鲜的
    refetchOnWindowFocus: true, // 窗口重新获得焦点时重新获取
  });

  // 删除聊天的 mutation
  const deleteChat = useMutation({
    mutationFn: deleteChatApi,
    onMutate: async (chatId: string) => {
      // 取消正在进行的查询以避免覆盖乐观更新
      await queryClient.cancelQueries({ queryKey: chatKeys.lists() });

      // 获取当前的聊天列表
      const previousChats = queryClient.getQueryData<ChatResponse[]>(chatKeys.lists());

      // 乐观更新：从列表中移除要删除的聊天
      queryClient.setQueryData<ChatResponse[]>(chatKeys.lists(), (old = []) =>
        old.filter(chat => chat.id !== chatId)
      );

      // 返回上下文对象，用于回滚
      return { previousChats };
    },
    onError: (error, chatId, context) => {
      // 发生错误时回滚到之前的状态
      if (context?.previousChats) {
        queryClient.setQueryData(chatKeys.lists(), context.previousChats);
      }
      console.error('删除对话失败:', error);
      toast.error('删除对话失败');
    },
    onSuccess: () => {
      toast.success('对话删除成功');
    },
    onSettled: () => {
      // 无论成功还是失败，都重新获取数据以确保同步
      queryClient.invalidateQueries({ queryKey: chatKeys.lists() });
    },
  });

  // 更新聊天标题的 mutation
  const updateChatTitle = useMutation({
    mutationFn: ({ chatId, title }: { chatId: string; title: string }) =>
      updateChatTitleApi(chatId, title),
    onMutate: async ({ chatId, title }) => {
      // 取消正在进行的查询
      await queryClient.cancelQueries({ queryKey: chatKeys.lists() });

      // 获取当前的聊天列表
      const previousChats = queryClient.getQueryData<ChatResponse[]>(chatKeys.lists());

      // 乐观更新：更新聊天标题
      queryClient.setQueryData<ChatResponse[]>(chatKeys.lists(), (old = []) =>
        old.map(chat =>
          chat.id === chatId ? { ...chat, title } : chat
        )
      );

      return { previousChats };
    },
    onError: (error, variables, context) => {
      // 发生错误时回滚
      if (context?.previousChats) {
        queryClient.setQueryData(chatKeys.lists(), context.previousChats);
      }
      console.error('更新对话标题失败:', error);
      toast.error('更新对话标题失败');
    },
    onSuccess: () => {
      toast.success('对话标题更新成功');
    },
    onSettled: () => {
      // 重新获取数据以确保同步
      queryClient.invalidateQueries({ queryKey: chatKeys.lists() });
    },
  });

  // 智能生成聊天标题的 mutation
  const generateSmartTitle = useMutation({
    mutationFn: (chatId: string) => generateSmartTitleApi(chatId),
    onMutate: async (chatId: string) => {
      // 取消正在进行的查询
      await queryClient.cancelQueries({ queryKey: chatKeys.lists() });

      // 获取当前的聊天列表
      const previousChats = queryClient.getQueryData<ChatResponse[]>(chatKeys.lists());

      return { previousChats };
    },
    onSuccess: (data, chatId) => {
      // 乐观更新：更新聊天标题
      queryClient.setQueryData<ChatResponse[]>(chatKeys.lists(), (old = []) =>
        old.map(chat =>
          chat.id === chatId ? { ...chat, title: data.title } : chat
        )
      );
    },
    onError: (error, chatId, context) => {
      // 发生错误时回滚
      if (context?.previousChats) {
        queryClient.setQueryData(chatKeys.lists(), context.previousChats);
      }
      console.error('❌ 智能标题生成失败:', error);
      // 不显示错误提示，因为这是后台操作
    },
    onSettled: () => {
      // 重新获取数据以确保同步
      queryClient.invalidateQueries({ queryKey: chatKeys.lists() });
    },
  });

  // 刷新聊天列表
  const refreshChats = () => {
    queryClient.invalidateQueries({ queryKey: chatKeys.lists() });
  };

  // 添加新聊天到缓存（用于新建聊天后的乐观更新）
  const addChatToCache = (newChat: ChatResponse) => {
    queryClient.setQueryData<ChatResponse[]>(chatKeys.lists(), (old = []) => [
      newChat,
      ...old,
    ]);
  };

  // 创建新聊天的 mutation
  const createChat = useMutation({
    mutationFn: async (title: string) => {
      const response = await fetch('/api/chats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ title }),
      });

      if (!response.ok) {
        throw new Error('创建对话失败');
      }

      const data = await response.json();
      return data.chat;
    },
    onSuccess: (newChat: ChatResponse) => {
      // 乐观更新：将新聊天添加到列表顶部
      queryClient.setQueryData<ChatResponse[]>(chatKeys.lists(), (old = []) => [
        newChat,
        ...old,
      ]);
      toast.success('新对话创建成功');
    },
    onError: (error) => {
      console.error('创建对话失败:', error);
      toast.error('创建对话失败');
    },
  });

  // 添加新聊天到缓存的增强版本，支持立即设置为当前聊天
  const addNewChatToCache = (newChat: ChatResponse, setAsCurrent = false) => {
    queryClient.setQueryData<ChatResponse[]>(chatKeys.lists(), (old = []) => [
      newChat,
      ...old,
    ]);

    // 如果需要立即刷新以确保 UI 同步
    if (setAsCurrent) {
      // 强制重新渲染以确保高亮状态正确
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: chatKeys.lists() });
      }, 100);
    }
  };

  return {
    // 数据
    chats,
    isLoading,
    error,

    // 操作
    deleteChat: deleteChat.mutate,
    updateChatTitle: updateChatTitle.mutate,
    generateSmartTitle: generateSmartTitle.mutate,
    createChat: createChat.mutate,
    refreshChats,
    addChatToCache,
    addNewChatToCache,
    refetch,

    // 状态
    isDeleting: deleteChat.isPending,
    isUpdating: updateChatTitle.isPending,
    isGeneratingTitle: generateSmartTitle.isPending,
    isCreating: createChat.isPending,
  };
}



/**
 * 单个聊天详情管理的自定义 hook
 */
export function useChatDetail(chatId: string | undefined) {
  const queryClient = useQueryClient();

  // 获取聊天详情的查询
  const {
    data: chatDetail,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: chatKeys.detail(chatId || ''),
    queryFn: () => fetchChatDetail(chatId!),
    enabled: !!chatId, // 只有当 chatId 存在时才执行查询
    staleTime: 1000 * 60 * 5, // 5分钟内认为数据是新鲜的
    refetchOnWindowFocus: false, // 聊天详情不需要在窗口重新获得焦点时重新获取
    retry: (failureCount, error: any) => {
      // 404 错误不重试
      if (error?.message?.includes('404') || error?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });

  // 转换消息格式以兼容 useChat hook
  const initialMessages = useMemo((): Message[] => {
    if (!chatDetail || !chatDetail.messages) {
      return [];
    }

    return chatDetail.messages.map((msg: any) => ({
      id: msg.id.toString(),
      role: msg.role,
      content: msg.content,
      createdAt: new Date(msg.createdAt),
    }));
  }, [chatDetail]);

  // 刷新聊天详情
  const refreshChatDetail = () => {
    if (chatId) {
      queryClient.invalidateQueries({ queryKey: chatKeys.detail(chatId) });
    }
  };

  // 更新聊天详情缓存（用于新消息后的乐观更新）
  const updateChatDetailCache = (newMessage: Message) => {
    if (!chatId || !chatDetail) return;

    queryClient.setQueryData<ChatWithMessages | null>(
      chatKeys.detail(chatId),
      (old) => {
        if (!old) return old;

        return {
          ...old,
          messages: [
            ...old.messages,
            {
              id: parseInt(newMessage.id),
              role: newMessage.role as 'user' | 'assistant',
              content: newMessage.content,
              createdAt: newMessage.createdAt || new Date(),
              chatId: old.id,
              userId: old.userId, // 使用现有聊天的 userId
            },
          ],
        };
      }
    );
  };

  return {
    // 数据
    chatDetail,
    initialMessages,
    isLoading,
    error,

    // 操作
    refreshChatDetail,
    updateChatDetailCache,
    refetch,

    // 状态
    exists: !!chatDetail,
    isEmpty: !isLoading && (!chatDetail || !chatDetail.messages || chatDetail.messages.length === 0),
  };
}
